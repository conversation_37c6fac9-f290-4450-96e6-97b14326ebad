#!/usr/bin/env python3
"""
测试运行器
重新组织项目结构后的测试入口
"""

import sys
import os

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

if __name__ == "__main__":
    # 运行测试
    import subprocess
    
    print("🧪 运行LLM配置测试...")
    result = subprocess.run([sys.executable, "test/test_llm_config.py"], 
                          cwd=os.path.dirname(__file__))
    
    if result.returncode == 0:
        print("✅ 测试通过!")
    else:
        print("❌ 测试失败!")
        sys.exit(1)
