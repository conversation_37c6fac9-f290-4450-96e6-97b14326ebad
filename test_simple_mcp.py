#!/usr/bin/env python3
"""
简单MCP测试 - 使用更保守的配置
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from mcp_use import MCPAgent, MCPClient
from src.llm_config import create_llm


async def test_simple_mcp():
    """测试简单的MCP功能"""
    print("🧪 简单MCP测试")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 显示当前配置
    print("🤖 当前LLM模型: Gemini 2.5 Flash")
    
    try:
        # 使用内网MCP服务器配置
        config = {
            "mcpServers": {
                "k8s": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }
        
        # 创建MCP客户端
        print("🔗 创建MCP客户端...")
        client = MCPClient.from_dict(config)
        
        # 创建Gemini 2.5 Flash最大能力LLM配置（禁用工具调用）
        print("🔧 创建Gemini 2.5 Flash LLM实例（保守模式）...")
        llm = create_llm(
            max_tokens=2048,
            model_kwargs={
                "tool_choice": "none",  # 禁用工具调用
                "parallel_tool_calls": False,
            }
        )
        
        print(f"✅ LLM创建成功: {llm.model_name}")
        
        # 创建Agent
        print("🤖 创建MCP Agent...")
        agent = MCPAgent(llm=llm, client=client, max_steps=3)
        
        # 测试简单查询
        print("💬 测试简单查询...")
        result = await agent.run(
            "你好，请简单介绍一下你的功能",
            max_steps=2,
        )
        
        print(f"📋 结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_tool_enabled_mcp():
    """测试启用工具的MCP功能"""
    print("\n🛠️  工具启用MCP测试")
    print("=" * 50)
    
    try:
        # 使用内网MCP服务器配置
        config = {
            "mcpServers": {
                "k8s": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }
        
        # 创建MCP客户端
        print("🔗 创建MCP客户端...")
        client = MCPClient.from_dict(config)
        
        # 创建启用工具的Gemini 2.5 Flash最大能力LLM配置
        print("🔧 创建Gemini 2.5 Flash LLM实例（工具启用模式）...")
        llm = create_llm(
            max_tokens=2048,
            model_kwargs={
                "tool_choice": "auto",  # 启用工具调用
                "parallel_tool_calls": False,
            }
        )
        
        print(f"✅ LLM创建成功: {llm.model_name}")
        
        # 创建Agent
        print("🤖 创建MCP Agent...")
        agent = MCPAgent(llm=llm, client=client, max_steps=3)
        
        # 测试K8s查询
        print("💬 测试Kubernetes查询...")
        result = await agent.run(
            "请获取集群信息",
            max_steps=3,
        )
        
        print(f"📋 结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 简单MCP测试套件")
    print("=" * 60)
    
    tests = [
        ("保守模式测试", test_simple_mcp),
        ("工具启用测试", test_tool_enabled_mcp),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行 {test_name}...")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed > 0:
        print("🎉 至少有部分功能正常工作!")
    else:
        print("⚠️  所有测试失败，需要进一步调试")


if __name__ == "__main__":
    asyncio.run(main())
