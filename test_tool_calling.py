#!/usr/bin/env python3
"""
工具调用功能测试脚本
验证VLLM服务器的工具调用支持
"""

import sys
import os
import asyncio
import json
from dotenv import load_dotenv

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.llm_config import create_llm, get_model_info


async def test_basic_tool_calling():
    """测试基本工具调用功能"""
    print("🧪 测试基本工具调用功能...")
    
    try:
        # 创建LLM实例
        llm = create_llm()
        
        # 定义一个简单的工具
        from langchain_core.tools import tool
        
        @tool
        def get_current_time() -> str:
            """获取当前时间"""
            from datetime import datetime
            return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 绑定工具到LLM
        llm_with_tools = llm.bind_tools([get_current_time])
        
        # 测试工具调用
        print("💬 发送工具调用请求...")
        response = await llm_with_tools.ainvoke("现在几点了？请告诉我当前时间。")
        
        print(f"🤖 LLM回复: {response.content}")
        
        # 检查是否有工具调用
        if hasattr(response, 'tool_calls') and response.tool_calls:
            print(f"🛠️  工具调用: {response.tool_calls}")
            return True
        else:
            print("⚠️  没有检测到工具调用")
            return False
            
    except Exception as e:
        print(f"❌ 工具调用测试失败: {e}")
        return False


async def test_vllm_tool_support():
    """直接测试VLLM服务器的工具调用支持"""
    print("\n🔍 直接测试VLLM服务器工具调用支持...")
    
    try:
        import requests
        
        server_url = os.getenv("QWEN3_MODEL_SERVER")
        if not server_url:
            print("❌ 未找到QWEN3_MODEL_SERVER配置")
            return False
        
        # 构造工具调用测试请求
        test_payload = {
            "model": "coder",
            "messages": [
                {"role": "user", "content": "现在几点了？"}
            ],
            "tools": [
                {
                    "type": "function",
                    "function": {
                        "name": "get_current_time",
                        "description": "获取当前时间",
                        "parameters": {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }
                    }
                }
            ],
            "tool_choice": "auto",
            "max_tokens": 100
        }
        
        # 确保URL格式正确
        api_url = f"{server_url.rstrip('/')}/v1/chat/completions"
        print(f"📡 发送请求到: {api_url}")
        response = requests.post(
            api_url,
            json=test_payload,
            timeout=30
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ VLLM服务器支持工具调用!")
            
            # 检查响应内容
            if 'choices' in result and result['choices']:
                choice = result['choices'][0]
                message = choice.get('message', {})
                
                print(f"💬 消息内容: {message.get('content', 'N/A')}")
                
                if 'tool_calls' in message:
                    print(f"🛠️  工具调用: {message['tool_calls']}")
                else:
                    print("ℹ️  没有工具调用（可能是正常的对话回复）")
            
            return True
            
        elif response.status_code == 400:
            error_info = response.json()
            print(f"❌ 工具调用不支持: {error_info.get('message', 'Unknown error')}")
            return False
        else:
            print(f"❌ 请求失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ VLLM测试失败: {e}")
        return False


async def test_mcp_integration():
    """测试MCP集成"""
    print("\n🔗 测试MCP集成...")
    
    try:
        from mcp_use import MCPAgent, MCPClient
        
        # 使用内网MCP服务器配置
        config = {
            "mcpServers": {
                "k8s": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }
        
        # 创建MCP客户端
        client = MCPClient.from_dict(config)
        
        # 创建LLM
        llm = create_llm()
        
        # 创建Agent
        agent = MCPAgent(llm=llm, client=client, max_steps=5)
        
        # 测试简单的K8s查询
        print("🔍 测试Kubernetes查询...")
        result = await agent.run(
            "请获取集群信息",
            max_steps=3,
        )
        
        print(f"📋 MCP结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ MCP集成测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 工具调用功能测试")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    # 显示当前配置
    provider = get_current_provider()
    print(f"🤖 当前LLM提供商: {provider.upper()}")
    
    if provider != "qwen3":
        print("⚠️  此测试专为Qwen3提供商设计")
        return
    
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("VLLM服务器工具调用支持", test_vllm_tool_support),
        ("基本工具调用功能", test_basic_tool_calling),
        ("MCP集成测试", test_mcp_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行 {test_name}...")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! 工具调用功能正常")
        print("\n💡 现在可以运行: uv run python main.py")
    else:
        print("⚠️  部分测试失败，请检查VLLM配置")
        print("\n🔧 确保VLLM启动时包含以下参数:")
        print("   --enable-auto-tool-choice")
        print("   --tool-call-parser hermes")


if __name__ == "__main__":
    asyncio.run(main())
