import asyncio
import os
from dotenv import load_dotenv
from mcp_use import MCPAgent, MCPClient
from .llm_config import create_llm, print_model_status


async def main():
    """Run the example using a configuration file."""
    # Load environment variables
    load_dotenv()

    # 打印当前LLM提供商状态
    print("=" * 60)
    print("🚀 MCP Agent 启动")
    print("=" * 60)
    print_model_status()
    print("=" * 60)

    # 创建Gemini 2.5 Flash最大能力LLM实例
    print("🔧 正在初始化 Gemini 2.5 Flash 最大能力LLM...")
    llm = create_llm()  # 使用统一的最大能力配置

    print(f"✅ LLM初始化完成")
    print(f"   模型: {llm.model_name}")
    print(f"   温度: {llm.temperature}")
    print(f"   最大Token: {llm.max_tokens}")
    print(f"   服务地址: {llm.openai_api_base}")
    print("=" * 60)

    # 连接K8s MCP服务器 - 这是系统运行的必要条件
    print("🔗 连接K8s MCP服务器...")

    config = {
        "mcpServers": {
            "k8s": {
                "type": "sse",
                "url": "http://ncpdev.gf.com.cn:31455/sse"
            }
        }
    }

    try:
        # Create MCPClient from config file
        client = MCPClient.from_dict(config)
        print("✅ MCP服务器连接成功")
    except Exception as e:
        print(f"❌ 致命错误: K8s MCP服务器连接失败")
        print(f"   错误详情: {e}")
        print(f"   服务器地址: http://ncpdev.gf.com.cn:31455/sse")
        print("")
        print("💡 K8s MCP Agent 的核心价值在于通过MCP工具管理真实的K8s集群")
        print("   没有MCP工具调用能力的Agent对K8s运维毫无意义")
        print("")
        print("🔧 请确保:")
        print("   1. MCP服务器正常运行")
        print("   2. 网络连接正常")
        print("   3. 服务器地址配置正确")
        print("")
        print("🚫 程序终止 - 无法在没有真实集群数据的情况下提供K8s管理服务")
        raise SystemExit(1) from e

    try:
        # Create agent with the client
        print("🤖 创建K8s MCP Agent...")
        agent = MCPAgent(llm=llm, client=client, max_steps=10)
        print("✅ K8s MCP Agent 创建成功")
    except Exception as e:
        print(f"❌ 致命错误: 无法创建K8s MCP Agent")
        print(f"   错误详情: {e}")
        print("")
        print("💡 Agent创建失败意味着无法进行K8s集群管理")
        print("� 程序终止 - 系统核心功能不可用")
        raise SystemExit(1) from e

    # 执行K8s集群信息查询 - 验证系统功能
    print("🔍 执行K8s集群信息查询...")
    try:
        result = await agent.run(
            "get the k8s cluster info",
            max_steps=5,
        )
        print("✅ K8s集群查询成功!")
        print(f"📋 真实集群数据: {result}")

    except Exception as e:
        print(f"❌ 致命错误: K8s集群查询失败")
        print(f"   错误详情: {e}")
        print("")
        print("💡 无法获取真实的K8s集群数据")
        print("   这违反了系统的数据真实性铁律")
        print("🚫 程序终止 - 无法提供基于真实数据的K8s管理服务")
        raise SystemExit(1) from e

if __name__ == "__main__":
    # Run the appropriate example
    asyncio.run(main())
