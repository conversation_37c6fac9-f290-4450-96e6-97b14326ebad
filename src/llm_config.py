"""
K8s MCP Agent LLM配置模块
专用于 Gemini 2.5 Flash 模型的最大能力配置
单一、统一的配置，始终使用最大上下文和输出能力
"""

import os
from typing import Dict, Any
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI

# 加载环境变量
load_dotenv()


class GeminiMaxConfig:
    """
    Gemini 2.5 Flash 最大能力配置
    始终使用最大上下文和输出能力的单一配置
    """

    # 固定使用的模型
    MODEL_NAME = "google/gemini-2.5-flash"

    # 最大能力配置
    MAX_INPUT_CONTEXT = 1048576      # 1,048,576 tokens 输入上下文
    MAX_OUTPUT_TOKENS = 32768        # 32,768 tokens 最大输出
    MAX_TIMEOUT = 600                # 600秒超时，适合大上下文处理

    # 安全停止序列
    SAFETY_STOP_SEQUENCES = [
        "```bash",
        "```sh",
        "```shell",
        "rm -rf",
        "kubectl delete",
        "docker rmi",
        "sudo rm"
    ]

    def __init__(self):
        """初始化Gemini最大能力配置管理器"""
        # 验证必要的环境变量
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY 环境变量未设置")

    def create_llm(self, **kwargs) -> ChatOpenAI:
        """
        创建Gemini 2.5 Flash最大能力LLM实例
        始终使用最大上下文和输出能力，无环境区分

        Args:
            **kwargs: 可选的覆盖参数（通常不需要）

        Returns:
            配置为最大能力的Gemini 2.5 Flash ChatOpenAI实例
        """
        return ChatOpenAI(
            model=self.MODEL_NAME,
            api_key=os.getenv("OPENROUTER_API_KEY"),
            base_url="https://openrouter.ai/api/v1",

            # 最大能力配置
            max_tokens=kwargs.get("max_tokens", self.MAX_OUTPUT_TOKENS),
            temperature=kwargs.get("temperature", 0.0),  # 确定性输出
            top_p=kwargs.get("top_p", 0.05),  # 高精度token选择

            # 稳定性配置
            frequency_penalty=kwargs.get("frequency_penalty", 0.0),
            presence_penalty=kwargs.get("presence_penalty", 0.0),
            streaming=kwargs.get("streaming", False),

            # 可靠性配置
            max_retries=kwargs.get("max_retries", 5),
            request_timeout=kwargs.get("request_timeout", self.MAX_TIMEOUT),

            # 安全配置
            stop=kwargs.get("stop", self.SAFETY_STOP_SEQUENCES),
            model_kwargs=kwargs.get("model_kwargs", {"seed": 42})
        )
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "provider": "OpenRouter",
            "model": self.MODEL_NAME,
            "api_key": os.getenv("OPENROUTER_API_KEY", "")[:10] + "..." if os.getenv("OPENROUTER_API_KEY") else "未设置",
            "base_url": "https://openrouter.ai/api/v1",
            "input_context": f"{self.MAX_INPUT_CONTEXT:,} tokens",
            "output_tokens": f"{self.MAX_OUTPUT_TOKENS:,} tokens",
            "timeout": f"{self.MAX_TIMEOUT}s",
            "features": ["工具调用", "最大上下文", "推理模式", "数学/编程", "K8s运维"],
            "configuration": "最大能力配置 - 无环境区分"
        }


# 全局配置实例
gemini_config = GeminiMaxConfig()


def create_llm(**kwargs) -> ChatOpenAI:
    """
    🎯 主要入口点：创建Gemini 2.5 Flash最大能力LLM实例

    这是创建LLM的唯一推荐方法，始终提供最大能力配置：
    - 输入上下文：1,048,576 tokens
    - 输出能力：32,768 tokens
    - 超时时间：600秒
    - 温度：0.0（确定性输出）
    - 针对K8s MCP操作优化

    Args:
        **kwargs: 可选的覆盖参数（通常不需要，系统已优化为最佳配置）

    Returns:
        配置为最大能力的Gemini 2.5 Flash ChatOpenAI实例

    Examples:
        >>> llm = create_llm()  # 推荐用法：使用最大能力配置
        >>> llm = create_llm(max_tokens=16384)  # 可选：自定义输出限制
    """
    return gemini_config.create_llm(**kwargs)


def get_model_info() -> Dict[str, Any]:
    """获取模型信息"""
    return gemini_config.get_model_info()


def print_model_status():
    """打印当前模型状态"""
    info = get_model_info()
    print(f"🤖 当前LLM模型: {info['model']}")
    print(f"🔗 服务地址: {info['base_url']}")
    print(f"🔑 API密钥: {info['api_key']}")
    print(f"📏 输入上下文: {info['input_context']}")
    print(f"📤 输出能力: {info['output_tokens']}")
    print(f"⏱️  超时设置: {info['timeout']}")
    print(f"🛠️  功能特性: {', '.join(info['features'])}")
    print(f"⚙️  配置模式: {info['configuration']}")
