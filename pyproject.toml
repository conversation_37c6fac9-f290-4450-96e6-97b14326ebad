[project]
name = "use-k8s-mcp"
version = "0.1.0"
description = "Kubernetes MCP Agent with configurable LLM providers"
readme = "doc/README.md"
requires-python = ">=3.13"
dependencies = [
    "langchain-openai>=0.3.24",
    "python-dotenv>=1.1.0",
    "mcp-use",
]

[project.scripts]
use-k8s-mcp = "main:main"
test-llm-config = "run_tests:main"
switch-provider = "switch_provider:main"

[tool.setuptools]
packages = ["src"]
package-dir = {"" = "src"}
