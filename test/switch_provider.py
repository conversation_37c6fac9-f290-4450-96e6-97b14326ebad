#!/usr/bin/env python3
"""
LLM提供商切换工具
用于快速切换OpenRouter和Qwen3提供商
"""

import os
import sys
import argparse
from pathlib import Path


def read_env_file(env_path: str = ".env") -> dict:
    """读取.env文件内容"""
    env_vars = {}
    
    if not os.path.exists(env_path):
        return env_vars
    
    with open(env_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key.strip()] = value.strip()
    
    return env_vars


def write_env_file(env_vars: dict, env_path: str = ".env"):
    """写入.env文件"""
    # 读取原始文件以保留注释和格式
    lines = []
    
    if os.path.exists(env_path):
        with open(env_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    
    # 更新变量值
    updated_lines = []
    updated_vars = set()
    
    for line in lines:
        stripped = line.strip()
        if stripped and not stripped.startswith('#') and '=' in stripped:
            key = stripped.split('=', 1)[0].strip()
            if key in env_vars:
                updated_lines.append(f"{key}={env_vars[key]}\n")
                updated_vars.add(key)
            else:
                updated_lines.append(line)
        else:
            updated_lines.append(line)
    
    # 添加新变量
    for key, value in env_vars.items():
        if key not in updated_vars:
            updated_lines.append(f"{key}={value}\n")
    
    # 写入文件
    with open(env_path, 'w', encoding='utf-8') as f:
        f.writelines(updated_lines)


def switch_to_openrouter():
    """切换到OpenRouter提供商"""
    print("🔄 切换到 OpenRouter 提供商...")
    
    env_vars = read_env_file()
    env_vars['LLM_PROVIDER'] = 'openrouter'
    write_env_file(env_vars)
    
    print("✅ 已切换到 OpenRouter")
    print("📋 当前配置:")
    print(f"   提供商: OpenRouter")
    print(f"   API密钥: {env_vars.get('OPENROUTER_API_KEY', '未设置')[:10]}...")
    print(f"   服务地址: https://openrouter.ai/api/v1")


def switch_to_qwen3():
    """切换到Qwen3提供商"""
    print("🔄 切换到 Qwen3 内网提供商...")
    
    env_vars = read_env_file()
    env_vars['LLM_PROVIDER'] = 'qwen3'
    write_env_file(env_vars)
    
    print("✅ 已切换到 Qwen3 内网模型")
    print("📋 当前配置:")
    print(f"   提供商: Qwen3-32B (内网)")
    print(f"   API密钥: {env_vars.get('QWEN3_API_KEY', 'EMPTY')}")
    print(f"   服务地址: {env_vars.get('QWEN3_MODEL_SERVER', '未设置')}")
    print(f"   模型名称: {env_vars.get('QWEN3_MODEL_NAME', 'coder')}")
    print(f"   思考模式: {env_vars.get('QWEN3_ENABLE_THINKING', 'false')}")


def show_current_config():
    """显示当前配置"""
    env_vars = read_env_file()
    provider = env_vars.get('LLM_PROVIDER', 'openrouter')
    
    print("📋 当前LLM配置:")
    print("=" * 50)
    print(f"🤖 提供商: {provider.upper()}")
    
    if provider == 'openrouter':
        api_key = env_vars.get('OPENROUTER_API_KEY', '未设置')
        print(f"🔑 API密钥: {api_key[:10]}..." if api_key != '未设置' else f"🔑 API密钥: {api_key}")
        print(f"🔗 服务地址: https://openrouter.ai/api/v1")
        print(f"📋 可用模型: GPT-3.5, GPT-4, Claude-3.5, Qwen2.5-Coder等")
        
    elif provider == 'qwen3':
        print(f"🔑 API密钥: {env_vars.get('QWEN3_API_KEY', 'EMPTY')}")
        print(f"🔗 服务地址: {env_vars.get('QWEN3_MODEL_SERVER', '未设置')}")
        print(f"🏷️  模型名称: {env_vars.get('QWEN3_MODEL_NAME', 'coder')}")
        print(f"🧠 思考模式: {env_vars.get('QWEN3_ENABLE_THINKING', 'false')}")
    
    print("=" * 50)


def validate_config():
    """验证当前配置"""
    env_vars = read_env_file()
    provider = env_vars.get('LLM_PROVIDER', 'openrouter')
    
    print("🔍 验证配置...")
    issues = []
    
    if provider == 'openrouter':
        if not env_vars.get('OPENROUTER_API_KEY'):
            issues.append("缺少 OPENROUTER_API_KEY")
    
    elif provider == 'qwen3':
        if not env_vars.get('QWEN3_MODEL_SERVER'):
            issues.append("缺少 QWEN3_MODEL_SERVER")
        
        # 检查服务器地址格式
        server = env_vars.get('QWEN3_MODEL_SERVER', '')
        if server and not (server.startswith('http://') or server.startswith('https://')):
            issues.append("QWEN3_MODEL_SERVER 应该以 http:// 或 https:// 开头")
    
    else:
        issues.append(f"不支持的提供商: {provider}")
    
    if issues:
        print("❌ 配置验证失败:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 配置验证通过")
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LLM提供商切换工具")
    parser.add_argument('action', choices=['openrouter', 'qwen3', 'status', 'validate'], 
                       help='操作: openrouter(切换到OpenRouter), qwen3(切换到Qwen3), status(显示状态), validate(验证配置)')
    
    args = parser.parse_args()
    
    print("🔧 LLM提供商切换工具")
    print("=" * 50)
    
    if args.action == 'openrouter':
        switch_to_openrouter()
    elif args.action == 'qwen3':
        switch_to_qwen3()
    elif args.action == 'status':
        show_current_config()
    elif args.action == 'validate':
        if not validate_config():
            sys.exit(1)
    
    print("\n💡 提示:")
    print("   - 使用 'python switch_provider.py status' 查看当前配置")
    print("   - 使用 'python switch_provider.py validate' 验证配置")
    print("   - 使用 'python test_llm_config.py' 测试配置")


if __name__ == "__main__":
    main()
