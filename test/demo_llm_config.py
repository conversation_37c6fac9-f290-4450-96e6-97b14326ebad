#!/usr/bin/env python3
"""
LLM配置系统演示脚本
展示如何在OpenRouter和Qwen3之间无缝切换
"""

import asyncio
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
from dotenv import load_dotenv
from llm_config import (
    create_llm,
    get_model_info,
    print_model_status
)


async def demo_basic_usage():
    """演示基本LLM使用"""
    print("🔧 基本LLM使用演示")
    print("-" * 40)
    
    # 创建默认LLM
    llm = create_llm()
    print(f"✅ 创建默认LLM: {llm.model_name}")
    print(f"   温度: {llm.temperature}")
    print(f"   最大Token: {llm.max_tokens}")
    print(f"   服务地址: {llm.openai_api_base}")
    
    # 测试简单对话
    try:
        print("\n🤖 测试简单对话...")
        response = await llm.ainvoke("你好，请简单介绍一下你自己")
        print(f"📝 回复: {response.content[:100]}...")
    except Exception as e:
        print(f"❌ 对话测试失败: {e}")
    
    print()


async def demo_k8s_usage():
    """演示Kubernetes专用LLM"""
    print("⚙️  Kubernetes专用LLM演示")
    print("-" * 40)
    
    # 创建生产环境K8s LLM
    k8s_llm = create_llm()
    print(f"✅ 创建K8s生产LLM: {k8s_llm.model_name}")
    print(f"   温度: {k8s_llm.temperature} (严格确定性)")
    print(f"   最大Token: {k8s_llm.max_tokens}")
    print(f"   安全停止序列: {len(k8s_llm.stop)} 个")
    
    # 测试K8s相关问题
    try:
        print("\n🔍 测试K8s问题...")
        k8s_question = "如何查看Kubernetes集群中所有Pod的状态？请给出kubectl命令"
        response = await k8s_llm.ainvoke(k8s_question)
        print(f"📝 K8s回复: {response.content[:150]}...")
    except Exception as e:
        print(f"❌ K8s测试失败: {e}")
    
    print()


def demo_provider_switching():
    """演示提供商切换"""
    print("🔄 提供商切换演示")
    print("-" * 40)
    
    info = get_model_info()
    current_provider = info.get('provider', 'Unknown')
    print(f"📋 当前提供商: {current_provider}")
    
    # 创建不同类型的LLM
    llm_types = ["default", "production", "development", "analysis"]
    
    for llm_type in llm_types:
        llm = create_llm(model_type=llm_type)
        print(f"   {llm_type:12}: {llm.model_name}")
    
    print()


async def demo_provider_comparison():
    """演示不同提供商的对比"""
    print("📊 提供商对比演示")
    print("-" * 40)
    
    info = get_model_info()

    print("🌐 当前使用OpenRouter服务")
    print("   优势: 模型丰富、性能强大、功能完整")
    print("   适用: 多样化需求、高质量输出")
    print(f"   输入上下文: {info.get('input_context', 'N/A')}")
    print(f"   输出能力: {info.get('output_tokens', 'N/A')}")
    print(f"   超时设置: {info.get('timeout', 'N/A')}")

    print()


async def main():
    """主演示函数"""
    print("🚀 LLM配置系统演示")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    # 显示当前配置状态
    print_model_status()
    print("=" * 60)
    
    # 运行各种演示
    await demo_basic_usage()
    await demo_k8s_usage()
    demo_provider_switching()
    await demo_provider_comparison()
    
    print("🎯 演示要点总结:")
    print("1. 通过修改 .env 中的 LLM_PROVIDER 可以无缝切换提供商")
    print("2. 代码无需修改，自动适配不同的LLM服务")
    print("3. 支持针对不同场景的专用配置（生产、开发、分析）")
    print("4. 内置安全机制，防止执行危险命令")
    print("5. 完整的测试和验证工具")
    
    print("\n💡 快速切换命令:")
    print("   切换到OpenRouter: python switch_provider.py openrouter")
    print("   切换到Qwen3:     python switch_provider.py qwen3")
    print("   查看当前状态:     python switch_provider.py status")
    print("   验证配置:         python switch_provider.py validate")
    print("   运行测试:         python test_llm_config.py")


if __name__ == "__main__":
    asyncio.run(main())
