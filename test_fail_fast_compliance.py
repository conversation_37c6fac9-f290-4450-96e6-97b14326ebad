#!/usr/bin/env python3
"""
Fail Fast原则合规性测试
验证K8s MCP Agent系统是否严格遵循Fail Fast异常处理原则
"""

import sys
import os
import time
import asyncio
from datetime import datetime

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.fail_fast_exceptions import (
    K8sAgentException, MCPConnectionError, ToolCallError, 
    DataValidationError, ClusterPermissionError, ClusterAccessError,
    FailFastValidator, create_exception_context,
    fail_fast_mcp_call, fail_fast_data_validation, fail_fast_cluster_access
)


class FailFastComplianceTest:
    """Fail Fast原则合规性测试套件"""
    
    def __init__(self):
        self.test_results = []
        self.violations = []
    
    def log_test_result(self, test_name: str, passed: bool, details: str = ""):
        """记录测试结果"""
        self.test_results.append({
            "test": test_name,
            "passed": passed,
            "details": details,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
    
    def test_exception_timing(self):
        """测试异常抛出的及时性（必须在100ms内）"""
        test_name = "异常抛出及时性测试"
        
        try:
            start_time = time.time()
            
            # 模拟一个应该快速失败的操作
            try:
                fail_fast_mcp_call("INVALID_TOOL", {})
            except ToolCallError:
                pass  # 预期的异常
            
            elapsed = time.time() - start_time
            
            if elapsed <= 0.1:  # 100ms
                self.log_test_result(test_name, True, f"异常抛出时间: {elapsed:.3f}s")
            else:
                self.log_test_result(test_name, False, f"异常抛出过慢: {elapsed:.3f}s > 0.1s")
                self.violations.append(f"异常抛出延迟: {elapsed:.3f}s")
                
        except Exception as e:
            self.log_test_result(test_name, False, f"测试异常: {e}")
    
    def test_no_fallback_behavior(self):
        """测试是否存在禁止的fallback行为"""
        test_name = "禁止fallback行为测试"
        
        try:
            # 测试工具调用失败时不应该有默认值
            try:
                result = fail_fast_mcp_call("FAILING_TOOL", {})
                # 如果到达这里，说明没有抛出异常，这是违规的
                self.log_test_result(test_name, False, "工具调用失败但未抛出异常")
                self.violations.append("工具调用失败时未抛出异常")
            except ToolCallError:
                # 这是预期的行为
                self.log_test_result(test_name, True, "工具调用失败正确抛出异常")
                
        except Exception as e:
            self.log_test_result(test_name, False, f"测试异常: {e}")
    
    def test_exception_context_completeness(self):
        """测试异常上下文信息的完整性"""
        test_name = "异常上下文完整性测试"
        
        try:
            try:
                fail_fast_mcp_call(
                    "TEST_TOOL", 
                    {"param1": "value1"},
                    cluster_name="test-cluster",
                    user_input="test query"
                )
            except ToolCallError as e:
                # 检查异常上下文是否完整
                context = e.context
                required_fields = ['operation', 'timestamp', 'mcp_tool', 'tool_params']
                missing_fields = []
                
                for field in required_fields:
                    if not getattr(context, field, None):
                        missing_fields.append(field)
                
                if missing_fields:
                    self.log_test_result(test_name, False, f"缺少上下文字段: {missing_fields}")
                    self.violations.append(f"异常上下文不完整: {missing_fields}")
                else:
                    self.log_test_result(test_name, True, "异常上下文完整")
                    
        except Exception as e:
            self.log_test_result(test_name, False, f"测试异常: {e}")
    
    def test_exception_propagation(self):
        """测试异常是否正确向上传播"""
        test_name = "异常向上传播测试"
        
        def level_3():
            fail_fast_data_validation({}, {"required_fields": ["name"]})
        
        def level_2():
            level_3()
        
        def level_1():
            level_2()
        
        try:
            level_1()
            # 如果到达这里，说明异常没有传播
            self.log_test_result(test_name, False, "异常未向上传播")
            self.violations.append("异常未向上传播")
        except DataValidationError:
            self.log_test_result(test_name, True, "异常正确向上传播")
        except Exception as e:
            self.log_test_result(test_name, False, f"异常类型错误: {type(e)}")
    
    def test_critical_failure_termination(self):
        """测试关键失败是否导致立即终止"""
        test_name = "关键失败立即终止测试"
        
        try:
            # 测试MCP连接失败
            try:
                context = create_exception_context(
                    operation="mcp_connection",
                    original_error="Connection refused"
                )
                raise MCPConnectionError("MCP连接失败", context)
            except MCPConnectionError:
                self.log_test_result(test_name, True, "MCP连接失败正确抛出异常")
            except Exception as e:
                self.log_test_result(test_name, False, f"异常类型错误: {type(e)}")
                
        except Exception as e:
            self.log_test_result(test_name, False, f"测试异常: {e}")
    
    def test_data_validation_strictness(self):
        """测试数据验证的严格性"""
        test_name = "数据验证严格性测试"
        
        test_cases = [
            (None, "空数据"),
            ({}, "空字典"),
            ({"incomplete": "data"}, "不完整数据")
        ]
        
        all_passed = True
        
        for test_data, description in test_cases:
            try:
                fail_fast_data_validation(
                    test_data, 
                    {"required_fields": ["name", "version", "status"]}
                )
                # 如果到达这里，说明验证没有失败
                all_passed = False
                self.violations.append(f"数据验证应该失败但通过了: {description}")
            except DataValidationError:
                # 这是预期的行为
                pass
            except Exception as e:
                all_passed = False
                self.violations.append(f"数据验证异常类型错误: {type(e)}")
        
        self.log_test_result(test_name, all_passed, 
                           "所有无效数据都正确触发异常" if all_passed else "部分验证失败")
    
    def test_permission_error_handling(self):
        """测试权限错误的处理"""
        test_name = "权限错误处理测试"
        
        try:
            try:
                fail_fast_cluster_access("restricted_cluster", "get_pods")
            except ClusterPermissionError:
                self.log_test_result(test_name, True, "权限错误正确抛出异常")
            except Exception as e:
                self.log_test_result(test_name, False, f"异常类型错误: {type(e)}")
                
        except Exception as e:
            self.log_test_result(test_name, False, f"测试异常: {e}")
    
    def run_all_tests(self):
        """运行所有合规性测试"""
        print("🧪 开始Fail Fast原则合规性测试")
        print("=" * 60)
        
        tests = [
            self.test_exception_timing,
            self.test_no_fallback_behavior,
            self.test_exception_context_completeness,
            self.test_exception_propagation,
            self.test_critical_failure_termination,
            self.test_data_validation_strictness,
            self.test_permission_error_handling
        ]
        
        for test in tests:
            try:
                test()
            except Exception as e:
                self.log_test_result(f"测试执行异常: {test.__name__}", False, str(e))
        
        self.generate_compliance_report()
    
    def generate_compliance_report(self):
        """生成合规性报告"""
        print("\n" + "=" * 60)
        print("📋 Fail Fast原则合规性报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"合规率: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.violations:
            print(f"\n⚠️  发现 {len(self.violations)} 个违规行为:")
            for i, violation in enumerate(self.violations, 1):
                print(f"  {i}. {violation}")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过! 系统完全符合Fail Fast原则")
        else:
            print(f"\n❌ 系统存在Fail Fast原则违规，需要修复")
        
        print("\n📊 详细测试结果:")
        for result in self.test_results:
            status = "✅" if result['passed'] else "❌"
            print(f"  {status} {result['test']}")
            if result['details']:
                print(f"      {result['details']}")


async def main():
    """主测试函数"""
    print("🚀 K8s MCP Agent Fail Fast原则合规性验证")
    print("=" * 60)
    print("📋 验证系统是否严格遵循数据真实性铁律中的Fail Fast原则")
    print("=" * 60)
    
    # 运行合规性测试
    compliance_test = FailFastComplianceTest()
    compliance_test.run_all_tests()
    
    print("\n💡 如果发现违规行为，请参考:")
    print("   - doc/数据真实性铁律.md")
    print("   - src/fail_fast_exceptions.py")
    print("   - 确保所有异常处理都符合Fail Fast原则")


if __name__ == "__main__":
    asyncio.run(main())
