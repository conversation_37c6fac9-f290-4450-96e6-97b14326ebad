#!/usr/bin/env python3
"""
修复工具调用问题的MCP测试
专门解决Qwen3工具调用参数格式问题
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from mcp_use import MCPAgent, MCPClient
from src.llm_config import create_mcp_compatible_llm, get_current_provider


async def test_conservative_mode():
    """测试保守模式（禁用工具调用）"""
    print("🔒 测试保守模式（禁用工具调用）")
    print("=" * 50)
    
    try:
        # 使用内网MCP服务器配置
        config = {
            "mcpServers": {
                "k8s": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }
        
        # 创建MCP客户端
        print("🔗 创建MCP客户端...")
        client = MCPClient.from_dict(config)
        
        # 强制使用保守模式
        print("🔧 创建LLM实例（强制保守模式）...")
        llm = create_mcp_compatible_llm("production", force_conservative=True)
        
        print(f"✅ LLM创建成功: {llm.model_name}")
        
        # 创建Agent
        print("🤖 创建MCP Agent...")
        agent = MCPAgent(llm=llm, client=client, max_steps=3)
        
        # 测试查询
        print("💬 测试查询...")
        result = await agent.run(
            "请获取集群信息",
            max_steps=3,
        )
        
        print(f"📋 结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 保守模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_adapter_mode():
    """测试适配器模式（修复工具调用）"""
    print("\n🛠️  测试适配器模式（修复工具调用）")
    print("=" * 50)
    
    try:
        # 使用内网MCP服务器配置
        config = {
            "mcpServers": {
                "k8s": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }
        
        # 创建MCP客户端
        print("🔗 创建MCP客户端...")
        client = MCPClient.from_dict(config)
        
        # 使用适配器模式
        print("🔧 创建LLM实例（适配器模式）...")
        llm = create_mcp_compatible_llm("production", force_conservative=False)
        
        print(f"✅ LLM创建成功: {llm.model_name}")
        
        # 创建Agent
        print("🤖 创建MCP Agent...")
        agent = MCPAgent(llm=llm, client=client, max_steps=3)
        
        # 测试K8s查询
        print("💬 测试Kubernetes查询...")
        result = await agent.run(
            "请获取集群信息",
            max_steps=3,
        )
        
        print(f"📋 结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 适配器模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_simple_conversation():
    """测试简单对话（不涉及工具调用）"""
    print("\n💬 测试简单对话")
    print("=" * 50)
    
    try:
        # 使用内网MCP服务器配置
        config = {
            "mcpServers": {
                "k8s": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }
        
        # 创建MCP客户端
        print("🔗 创建MCP客户端...")
        client = MCPClient.from_dict(config)
        
        # 使用保守模式
        print("🔧 创建LLM实例（保守模式）...")
        llm = create_mcp_compatible_llm("production", force_conservative=True)
        
        print(f"✅ LLM创建成功: {llm.model_name}")
        
        # 创建Agent
        print("🤖 创建MCP Agent...")
        agent = MCPAgent(llm=llm, client=client, max_steps=2)
        
        # 测试简单对话
        print("💬 测试简单对话...")
        result = await agent.run(
            "你好，请简单介绍一下你的功能",
            max_steps=2,
        )
        
        print(f"📋 结果: {result[:300]}...")
        return True
        
    except Exception as e:
        print(f"❌ 简单对话测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🧪 修复工具调用问题的MCP测试")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    # 显示当前配置
    provider = get_current_provider()
    print(f"🤖 当前LLM提供商: {provider.upper()}")
    
    if provider != "qwen3":
        print("⚠️  此测试专为Qwen3提供商设计")
        return
    
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("简单对话测试", test_simple_conversation),
        ("保守模式测试", test_conservative_mode),
        ("适配器模式测试", test_adapter_mode),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行 {test_name}...")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed > 0:
        print("🎉 至少有部分功能正常工作!")
        if passed == total:
            print("🎯 所有测试通过，工具调用问题已解决!")
        else:
            print("💡 建议使用保守模式以确保稳定性")
    else:
        print("⚠️  所有测试失败，需要进一步调试")


if __name__ == "__main__":
    asyncio.run(main())
