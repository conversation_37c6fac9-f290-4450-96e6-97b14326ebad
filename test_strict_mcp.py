#!/usr/bin/env python3
"""
严格模式MCP测试
确保只使用真实的MCP工具调用，不允许模拟数据
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# 添加 src 目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from mcp_use import MCPAgent, MCPClient
from src.strict_mcp_config import create_strict_mcp_llm, validate_mcp_response, format_real_data_only


async def test_strict_mcp_mode():
    """测试严格模式MCP"""
    print("🔒 严格模式MCP测试")
    print("=" * 60)
    print("📋 规则: 只使用真实MCP工具数据，不允许模拟")
    print("=" * 60)
    
    try:
        # 使用内网MCP服务器配置
        config = {
            "mcpServers": {
                "k8s": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }
        
        # 创建MCP客户端
        print("🔗 创建MCP客户端...")
        client = MCPClient.from_dict(config)
        
        # 创建严格模式LLM
        print("🔧 创建严格模式LLM...")
        llm = create_strict_mcp_llm()
        
        print(f"✅ LLM创建成功: {llm.model_name}")
        print(f"   温度: {llm.temperature} (确定性)")
        print(f"   最大Token: {llm.max_tokens} (限制输出)")
        
        # 创建Agent
        print("🤖 创建MCP Agent...")
        agent = MCPAgent(llm=llm, client=client, max_steps=3)
        
        # 测试真实的集群信息查询
        print("\n💬 测试真实集群信息查询...")
        print("🔍 查询: 获取真实的Kubernetes集群信息")
        
        result = await agent.run(
            "请使用GET_CLUSTER_INFO工具获取真实的Kubernetes集群信息，不要编造任何数据",
            max_steps=3,
        )
        
        print("\n📋 原始响应:")
        print("-" * 40)
        print(result)
        print("-" * 40)
        
        # 验证响应的真实性
        print("\n🔍 验证响应真实性...")
        validation = validate_mcp_response(result)
        
        print(f"   工具调用: {'✅' if validation['has_tool_calls'] else '❌'}")
        print(f"   观察结果: {'✅' if validation['has_observations'] else '❌'}")
        print(f"   真实数据: {'✅' if validation['is_real_data'] else '❌'}")
        print(f"   可能模拟: {'⚠️' if validation['potential_simulation'] else '✅'}")
        
        if validation["warnings"]:
            print("⚠️ 警告:")
            for warning in validation["warnings"]:
                print(f"   - {warning}")
        
        # 格式化真实数据
        print("\n📊 格式化真实数据:")
        print("-" * 40)
        formatted_data = format_real_data_only(result, validation)
        print(formatted_data)
        print("-" * 40)
        
        return validation["is_real_data"]
        
    except Exception as e:
        print(f"❌ 严格模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_real_tool_verification():
    """验证真实工具调用"""
    print("\n🔧 验证真实工具调用")
    print("=" * 60)
    
    try:
        # 直接测试MCP客户端
        config = {
            "mcpServers": {
                "k8s": {
                    "type": "sse",
                    "url": "http://ncpdev.gf.com.cn:31455/sse"
                }
            }
        }
        
        print("🔗 创建MCP客户端...")
        client = MCPClient.from_dict(config)
        
        # 初始化客户端
        print("🔄 初始化MCP会话...")
        sessions = await client.create_all_sessions()
        
        print(f"✅ 创建了 {len(sessions)} 个MCP会话")
        
        # 列出可用工具
        for session_name, session in sessions.items():
            print(f"\n📋 会话 '{session_name}' 可用工具:")
            tools = await session.list_tools()
            for tool in tools.tools[:5]:  # 只显示前5个
                print(f"   - {tool.name}: {tool.description}")
            if len(tools.tools) > 5:
                print(f"   ... 还有 {len(tools.tools) - 5} 个工具")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具验证失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 严格模式MCP测试套件")
    print("=" * 60)
    print("🎯 目标: 确保只使用真实MCP工具数据")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    
    # 运行测试
    tests = [
        ("真实工具验证", test_real_tool_verification),
        ("严格模式测试", test_strict_mcp_mode),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行 {test_name}...")
        try:
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 严格模式验证通过!")
        print("💡 现在可以确保只使用真实的MCP工具数据")
    else:
        print("⚠️ 部分测试失败，请检查MCP配置")
    
    print("\n📋 使用建议:")
    print("1. 确保MCP服务器正常运行")
    print("2. 验证工具调用返回真实数据")
    print("3. 避免使用可能产生模拟数据的提示词")


if __name__ == "__main__":
    asyncio.run(main())
