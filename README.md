# use-k8s-mcp

Kubernetes MCP Agent with Gemini 2.5 Flash

## 📁 项目结构

```
use-k8s-mcp/
├── src/                    # 源代码
│   ├── llm_config.py      # 核心LLM配置模块 (Gemini 2.5 Flash)
│   ├── main.py            # 主程序
│   └── fail_fast_exceptions.py # Fail-fast异常处理
├── test/                   # 测试
│   └── test_llm_config.py # LLM配置测试
├── doc/                    # 文档
│   ├── README.md          # 详细说明文档
│   ├── MCP_TROUBLESHOOTING.md # MCP故障排除
│   └── TROUBLESHOOTING.md # 故障排除
├── script/                 # 部署脚本
│   └── deploy-vllm.sh     # VLLM部署脚本
├── .env                    # 环境配置
├── main.py                 # 主程序入口点
└── pyproject.toml         # 项目配置
```

## 🚀 快速开始

### 1. 安装依赖
```bash
uv sync
```

### 2. 配置环境
创建 `.env` 文件，配置 Gemini 2.5 Flash：
```bash
# Gemini 2.5 Flash 配置
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

### 3. 运行程序
```bash
# 运行主程序
uv run python main.py
```

> 💡 **提示**: 如果遇到MCP连接错误，请查看 [MCP故障排除指南](doc/MCP_TROUBLESHOOTING.md)

### 4. 运行测试
```bash
# 运行配置测试
uv run python test/test_llm_config.py
```

## 📚 文档

详细文档请查看 `doc/` 目录：

- **[详细说明](doc/README.md)** - 完整的项目说明
- **[MCP故障排除](doc/MCP_TROUBLESHOOTING.md)** - MCP连接问题解决
- **[故障排除](doc/TROUBLESHOOTING.md)** - 其他常见问题

## 🔧 核心特性

- **🤖 专用模型**: 专门使用 Gemini 2.5 Flash，具备强大的推理能力
- **🔧 工具调用**: 完美兼容 MCP 工具调用
- **📏 大上下文**: 支持 1,048,576 tokens，适合复杂的 K8s 运维
- **⚡ Fail Fast**: 严格的异常处理，确保数据真实性
- **🛡️ 安全可靠**: 绝不编造集群数据，只使用真实的 MCP 工具返回

## 🔧 开发

### 项目结构说明

- **`src/`** - 所有源代码文件
- **`test/`** - 测试文件
- **`doc/`** - 项目文档
- **`script/`** - 部署和运维脚本

### 运行测试

```bash
# 运行配置测试
uv run python test/test_llm_config.py
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
